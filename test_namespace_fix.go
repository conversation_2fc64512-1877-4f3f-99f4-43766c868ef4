package main

import (
	"fmt"
	"os"

	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes/scheme"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/cache"

	"github.com/openkruise/kruise/apis"
	"github.com/openkruise/kruise/pkg/controller/daemonset"
	"github.com/openkruise/kruise/pkg/controller/statefulset"
)

func main() {
	// Test the fix for the multiNamespaceInformer issue
	fmt.Println("Testing namespace-specific cache configuration...")

	// Setup scheme
	s := runtime.NewScheme()
	if err := scheme.AddToScheme(s); err != nil {
		fmt.Printf("Failed to add k8s scheme: %v\n", err)
		os.Exit(1)
	}
	if err := apis.AddToScheme(s); err != nil {
		fmt.Printf("Failed to add kruise scheme: %v\n", err)
		os.Exit(1)
	}

	// Get config
	cfg := ctrl.GetConfigOrDie()

	// Test with namespace-specific cache (this should trigger the multiNamespaceInformer case)
	fmt.Println("Testing with namespace-specific cache...")
	mgr, err := ctrl.NewManager(cfg, ctrl.Options{
		Scheme: s,
		Cache: cache.Options{
			DefaultNamespaces: map[string]cache.Config{
				"kruise-system": {},
			},
		},
	})
	if err != nil {
		fmt.Printf("Failed to create manager: %v\n", err)
		os.Exit(1)
	}

	// Test DaemonSet controller
	fmt.Println("Testing DaemonSet controller...")
	if err := daemonset.Add(mgr); err != nil {
		fmt.Printf("DaemonSet controller failed: %v\n", err)
		os.Exit(1)
	}
	fmt.Println("DaemonSet controller added successfully!")

	// Test StatefulSet controller
	fmt.Println("Testing StatefulSet controller...")
	if err := statefulset.Add(mgr); err != nil {
		fmt.Printf("StatefulSet controller failed: %v\n", err)
		os.Exit(1)
	}
	fmt.Println("StatefulSet controller added successfully!")

	fmt.Println("All tests passed! The fix works correctly.")
}
